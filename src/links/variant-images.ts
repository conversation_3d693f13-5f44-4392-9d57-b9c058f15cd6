import { defineLink } from "@medusajs/framework/utils";
import ExtendedVariantModule from '../modules/extended/variants'
import ProductModule from "@medusajs/medusa/product";

export default defineLink(
    {
        linkable: ProductModule.linkable.productVariant,
        field: "id",
        isList: true
    },
    {
        ...ExtendedVariantModule.linkable.variantImage.id,
        primaryKey: "variant_id"
    },
    { readOnly: true }
)
