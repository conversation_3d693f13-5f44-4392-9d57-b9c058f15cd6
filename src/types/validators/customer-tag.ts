import { createFindParams } from '@medusajs/medusa/api/utils/validators'
import { applyAndAndOrOperators } from '@medusajs/medusa/api/utils/common-validators/common'
import { createOperatorMap } from '@medusajs/medusa/api/utils/validators'
import { z } from 'zod'

export const CustomerTagDataSchema = z.object({
  id: z.string().optional(),
  value: z.string().min(1, 'Tag value is required'),
})

export const CreateCustomerTagSchema = CustomerTagDataSchema.omit({
  id: true,
})

export const UpdateCustomerTagSchema = z.object({
  value: z.string().min(1, 'Tag value is required'),
})

export const DeleteCustomerTagSchema = z.object({
  id: z.string(),
})

export type CustomerTagDataValidated = z.infer<typeof CustomerTagDataSchema>
export type CreateCustomerTagValidated = z.infer<typeof CreateCustomerTagSchema>
export type UpdateCustomerTagValidated = z.infer<typeof UpdateCustomerTagSchema>
export type DeleteCustomerTagValidated = z.infer<typeof DeleteCustomerTagSchema>

export const defaultCustomerTagFields = [
  'id',
  'value',
  'created_at',
  'updated_at',
]

export const ListCustomerTagsParamsDirectFields = applyAndAndOrOperators(
  z.object({
    id: createOperatorMap().optional(),
    value: createOperatorMap().optional(),
    created_at: createOperatorMap().optional(),
    updated_at: createOperatorMap().optional(),
  })
)

export const ListCustomerTagsParams = createFindParams({
  limit: 10,
  offset: 0,
  order: '-updated_at',
}).merge(ListCustomerTagsParamsDirectFields)

export type ListCustomerTagsParamsType = z.infer<typeof ListCustomerTagsParams>
