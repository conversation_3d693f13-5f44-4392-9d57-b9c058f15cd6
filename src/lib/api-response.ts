/**
 * Standard API response format
 */
export interface ApiResponse<T> {
  data: T | null
  message: string
  status: number
  success: boolean
}

/**
 * Creates a success response with standard format
 * @param data Response data
 * @param message Success message (defaults to "OK")
 * @returns Standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message = 'OK'
): ApiResponse<T> {
  return {
    data,
    message,
    status: 200,
    success: true,
  }
}

/**
 * Creates an error response with standard format
 * @param message Error message
 * @param status HTTP status code (defaults to 200 as per requirements)
 * @returns Standardized error response
 */
export function createErrorResponse(
  message: string,
  status: number
): ApiResponse<null> {
  return {
    data: null,
    message,
    status,
    success: false,
  }
}

/**
 * Utility function to send a standardized API response
 * @param res Response object
 * @param response Standardized API response
 */
export function sendApiResponse(res: any, response: ApiResponse<any>): void {
  res.status(200).json(response)
}
