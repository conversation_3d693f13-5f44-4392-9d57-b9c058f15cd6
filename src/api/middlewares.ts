import { defineMiddlewares } from '@medusajs/framework/http'
import { customerTagRoutesMiddlewares } from './admin/extended/customers/middleware'
import { variantRoutesMiddlewares } from './admin/extended/variants/middleware'
import { productRoutesMiddlewares } from './admin/product-and-bundles/middleware'

export default defineMiddlewares({
  routes: [
    ...variantRoutesMiddlewares,
    ...customerTagRoutesMiddlewares,
    ...productRoutesMiddlewares
  ],
})
