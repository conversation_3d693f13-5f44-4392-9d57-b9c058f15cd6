import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http'
import { ContainerRegistrationKeys } from '@medusajs/framework/utils'
import { createErrorResponse, createSuccessResponse, sendApiResponse } from '../../../../lib/api-response'

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // First, get the bundle variant with extended data
    const { data: bundleVariants } = await query.graph({
      entity: 'product_variants',
      fields: ["*", 'extended_product_variants.*', 'product.*'],
      filters: { id }
    })

    // Check if bundle variant exists
    if (!bundleVariants || !bundleVariants.length) {
      return sendApiResponse(
        res,
        createErrorResponse(`Bundle variant with id ${id} not found`, 404)
      )
    }

    const bundleVariant = bundleVariants[0]

    // Check if it's actually a bundle
    if (!bundleVariant?.extended_product_variants?.bundle_type) {
      return sendApiResponse(
        res,
        createErrorResponse(`Variant with id ${id} is not a bundle`, 400)
      )
    }

    // Get inventory items associated with this bundle
    const { data: [bundleProductsInventories] } = await query.graph({
      entity: 'product_variants',
      fields: ['inventory_items.*'],
      filters: { id: 'variant_01JVBZGQ0XA4PMPBYKG2B92N5F' }
    })

    const inventoryItemIds = (bundleProductsInventories?.inventory_items?.map(item => item?.inventory_item_id).filter(Boolean) || []) as string[]

    const { data: bundleProducts } = await query.graph({
      entity: 'inventory_items',
      fields: ['*', 'variants.*', 'variants.extended_product_variants.*', 'variants.product.*'],
      filters: { id: inventoryItemIds }
    })

    return sendApiResponse(res, createSuccessResponse({
      bundle: {
        ...bundleVariant,

        bundle_products: bundleProducts.flatMap(item => item.variants?.map(variant => {
          return { ...variant, ...item }
        })).filter(variant => !variant?.extended_product_variants?.bundle_type)
      }
    }))
  } catch (error) {
    return sendApiResponse(
      res,
      createErrorResponse(`Error fetching bundle: ${error.message}`, 500)
    )
  }
}
