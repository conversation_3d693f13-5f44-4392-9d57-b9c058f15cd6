import {
  MiddlewareRoute,
  validateAndTransformQuery
} from '@medusajs/framework/http'
import { AdminGetProductsParams, listProductQueryConfig } from './validators'

export const productRoutesMiddlewares: MiddlewareRoute[] = [
  {
    method: 'GET',
    matcher: '/admin/product-and-bundles',
    middlewares: [validateAndTransformQuery(AdminGetProductsParams, listProductQueryConfig)],
  }
]

export default {
  routes: productRoutesMiddlewares,
}
