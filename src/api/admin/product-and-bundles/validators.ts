import { FilterableProductProps, OperatorMap } from "@medusajs/framework/types"
import { isPresent } from '@medusajs/framework/utils'
import { applyAndAndOrOperators, booleanString } from '@medusajs/medusa/api/utils/common-validators/common'
import { createFindParams, createOperatorMap } from '@medusajs/medusa/api/utils/validators'
import { z } from 'zod'


export const StoreGetProductParamsDirectFields = z.object({
  q: z.string().optional(),
  id: z.union([z.string(), z.array(z.string())]).optional(),
  title: z.union([z.string(), z.array(z.string())]).optional(),
  handle: z.union([z.string(), z.array(z.string())]).optional(),
  is_giftcard: booleanString().optional(),
  category_id: z.union([z.string(), z.array(z.string())]).optional(),
  external_id: z.union([z.string(), z.array(z.string())]).optional(),
  collection_id: z.union([z.string(), z.array(z.string())]).optional(),
  tag_id: z.union([z.string(), z.array(z.string())]).optional(),
  type_id: z.union([z.string(), z.array(z.string())]).optional(),
  created_at: createOperatorMap().optional(),
  updated_at: createOperatorMap().optional(),
  deleted_at: createOperatorMap().optional(),
})

export const GetProductsParams = z
  .object({
    sales_channel_id: z.union([z.string(), z.array(z.string())]).optional(),
  })
  .merge(StoreGetProductParamsDirectFields)

export const AdminGetProductVariantsParamsFields = z.object({
  q: z.string().optional(),
  id: z.union([z.string(), z.array(z.string())]).optional(),
  manage_inventory: booleanString().optional(),
  allow_backorder: booleanString().optional(),
  created_at: createOperatorMap().optional(),
  updated_at: createOperatorMap().optional(),
  deleted_at: createOperatorMap().optional(),
  extended_product_variants: z.object({
    bundle_type: createOperatorMap(z.enum(['combo', 'byob', 'variant'])).optional(),
  }).optional(),
})

export type AdminGetProductVariantsParamsType = z.infer<
  typeof AdminGetProductVariantsParams
>
export const AdminGetProductVariantsParams = createFindParams({
  offset: 0,
  limit: 50,
})
  .merge(AdminGetProductVariantsParamsFields)
  .merge(applyAndAndOrOperators(AdminGetProductVariantsParamsFields))

export const AdminGetProductsParamsDirectFields = z.object({
  variants: AdminGetProductVariantsParamsFields.merge(
    applyAndAndOrOperators(AdminGetProductVariantsParamsFields)
  ).optional(),
})


type HttpProductFilters = FilterableProductProps & {
  tag_id?: string | string[]
  category_id?: string | string[]
}

export const transformProductParams = (
  data: HttpProductFilters
): FilterableProductProps => {
  const res: HttpProductFilters = {
    ...data,
  }

  if (isPresent(data.tag_id)) {
    res.tags = { id: data.tag_id as string[] }
    delete res.tag_id
  }

  if (isPresent(data.category_id)) {
    res.categories = { id: data.category_id as OperatorMap<string> }
    delete res.category_id
  }

  return res as FilterableProductProps
}

export type AdminGetProductsParamsType = z.infer<typeof AdminGetProductsParams>
export const AdminGetProductsParams = createFindParams({
  offset: 0,
  limit: 50,
})
  .merge(AdminGetProductsParamsDirectFields)
  .merge(
    z
      .object({
        price_list_id: z.string().array().optional(),
      })
      .merge(applyAndAndOrOperators(AdminGetProductsParamsDirectFields))
      .merge(GetProductsParams)
  )
  .transform(transformProductParams)


export const defaultAdminProductFields = [
  "id",
  "title",
  "subtitle",
  "status",
  "external_id",
  "description",
  "handle",
  "is_giftcard",
  "discountable",
  "thumbnail",
  "collection_id",
  "type_id",
  "weight",
  "length",
  "height",
  "width",
  "hs_code",
  "origin_country",
  "mid_code",
  "material",
  "created_at",
  "updated_at",
  "deleted_at",
  "metadata",
  "*type",
  "*collection",
  "*options",
  "*options.values",
  "*tags",
  "*images",
  "*variants",
  "*variants.prices",
  "variants.prices.price_rules.value",
  "variants.prices.price_rules.attribute",
  "*variants.options",
  "*sales_channels",
]

export const retrieveProductQueryConfig = {
  defaults: defaultAdminProductFields,
  isList: false,
}

export const listProductQueryConfig = {
  ...retrieveProductQueryConfig,
  defaultLimit: 50,
  isList: true,
}
