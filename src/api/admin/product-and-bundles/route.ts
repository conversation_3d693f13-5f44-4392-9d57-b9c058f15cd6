import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http'
import { ContainerRegistrationKeys } from '@medusajs/framework/utils'

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Execute the query
    const { data, metadata } = await query.index({
      entity: 'product',
      ...req.queryConfig,
      filters: req.filterableFields,
    })

    return res.status(200).json({
      products: data,
      count: metadata?.estimate_count || 0,
      offset: metadata?.skip || 0,
      limit: metadata?.take || 50,
    })
  } catch (error) {
    return res.status(500).json({
      message: `Error fetching products: ${error.message}`
    })
  }
}