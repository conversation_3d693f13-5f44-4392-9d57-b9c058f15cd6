import { ProductVariantDTO } from '@medusajs/framework/types'

import { z } from 'zod'
export type BundleType = 'combo' | 'byob'
export type DiscountType = 'flat_price' | 'percentage_off'

export const createExtendedProductVariantStepSchema = z
  .object({
    discounted_price: z.number().optional(),
    sdd: z.boolean().optional(),
    gtin: z.string().optional(),
    google_product_category: z.string().optional(),
    primary_image_id: z.string().optional(),
    images: z.array(z.object({
      url: z.string(),
      rank: z.number()
    })).optional(),

    bundle_type: z.enum(['combo', 'byob']).optional(),
    max_bundle_per_order_quantity: z.number().optional(),
    products_per_bundle: z.number().optional(),
    online_payment_only: z.boolean().optional(),
    bundle_discount_type: z.enum(['flat_price', 'percentage_off']).optional(),
    bundle_discount: z.number().optional(),
  })
  .refine(
    data => {
      // If bundle_type is provided, products_per_bundle must also be provided
      if (
        data.bundle_type !== undefined &&
        data.products_per_bundle === undefined
      ) {
        return false
      }
      return true
    },
    {
      message: 'products_per_bundle is required when bundle_type is provided',
      path: ['products_per_bundle'], // Path to the field that caused the error
    }
  )

export type createExtendedProductVariantStepType = z.infer<
  typeof createExtendedProductVariantStepSchema
>

export type createExtendedProductVariantWorkflowType = {
  product_variant: ProductVariantDTO
  extended_data: createExtendedProductVariantStepType
}

export type UpdateExtendedVariantWorkflowInputType = {
  product_variant: ProductVariantDTO
  extended_data: UpdateExtendedVariantStepType
}

export const UpdateExtendedVariantStepSchema = z.object({
  discounted_price: z.number().optional(),
  sdd: z.boolean().optional(),
  gtin: z.string().optional(),
  google_product_category: z.string().optional(),
  primary_image_id: z.string().optional(),
  images: z.array(z.object({
    url: z.string(),
    rank: z.number()
  })).optional(),

  max_bundle_per_order_quantity: z.number().optional(),
  products_per_bundle: z.number().optional(),
  online_payment_only: z.boolean().optional(),
  bundle_discount_type: z.enum(['flat_price', 'percentage_off']).optional(),
  bundle_discount: z.number().optional(),
})

export type UpdateExtendedVariantStepType = z.infer<
  typeof UpdateExtendedVariantStepSchema
>

export interface ExtendedData {
  gtin?: string
  [key: string]: any
}

export interface OptionValueData {
  [optionValue: string]: {
    additional_data: ExtendedData
  }
}

export interface OptionData {
  [optionTitle: string]: OptionValueData[]
}

export interface AdditionalDataStructure {
  options: OptionData[]
}