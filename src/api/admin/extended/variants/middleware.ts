import {
  MiddlewareRoute
} from '@medusajs/framework/http'
import { z } from 'zod'

import { createExtendedProductVariantStepSchema, UpdateExtendedVariantStepSchema } from './validators'

export const variantRoutesMiddlewares: MiddlewareRoute[] = [
  {
    method: 'POST',
    matcher: '/admin/products',
    additionalDataValidator: {
      options: z.array(
        z.record(
          z.string(),
          z.array(
            z.record(
              z.string(),
              z.object({
                additional_data: z
                  .object({
                    discounted_price: z.number().optional(),
                    sdd: z.boolean().optional(),
                    gtin: z.string().optional(),
                    google_product_category: z.string().optional(),
                  images : z.array(z.object({
                    url : z.string(),
                    rank : z.number()
                  })).optional(),

                    bundle_type: z.enum(['combo', 'byob']).optional(),
                    max_bundle_per_order_quantity: z.number().optional(),
                    products_per_bundle: z.number().optional(),
                    online_payment_only: z.boolean().optional(),
                    bundle_discount_type: z
                      .enum(['flat_price', 'percentage_off'])
                      .optional(),
                    bundle_discount: z.number().optional(),
                  })
                  .refine(
                    data => {
                      if (
                        data.bundle_type !== undefined &&
                        data.products_per_bundle === undefined
                      ) {
                        return false
                      }
                      return true
                    },
                    {
                      message:
                        'products_per_bundle is required when bundle_type is provided',
                      path: ['products_per_bundle'],
                    }
                  ),
              })
            )
          )
        )
      ),
    },
  },
  {
    method: 'POST',
    matcher: '/admin/products/:id/variants',
    additionalDataValidator: createExtendedProductVariantStepSchema._def.schema.shape,
  },
  {
    method: 'POST',
    matcher: '/admin/products/:id/variants/:variant_id',
    additionalDataValidator: UpdateExtendedVariantStepSchema.shape,
  },
]

export default {
  routes: variantRoutesMiddlewares,
}
