import {
  MedusaNextFunction,
  MedusaRequest,
  MedusaResponse,
  MiddlewareRoute,
} from '@medusajs/framework/http'
import {
  validateAndTransformBody,
  validateAndTransformQuery,
} from '@medusajs/framework/http'
import {
  CreateCustomerTagSchema,
  UpdateCustomerTagSchema,
  ListCustomerTagsParams,
} from './tags/validators'
import { CreateCustomer } from '@medusajs/medusa/api/admin/customers/validators'
import { z } from 'zod'

const extendedCustomerMiddleware = (
  req: MedusaRequest,
  res: MedusaResponse,
  next: MedusaNextFunction
) => {
  if (req.allowed) req.allowed?.push('extended_customer')
  else req.allowed = ['extended_customer']
  next()
}

export const customerTagRoutesMiddlewares: MiddlewareRoute[] = [
  {
    method: 'POST',
    matcher: '/admin/customers',
    additionalDataValidator: {
      customer_tag_ids: z.array(z.string()).optional(),
    },
    middlewares: [
      validateAndTransformBody(
        CreateCustomer.merge(
          z.object({
            phone: z.string(),
            additional_data: z
              .object({
                customer_tag_ids: z.array(z.string()).optional(),
              })
              .optional(),
          })
        )
      ),
    ],
  },

  {
    method: 'POST',
    matcher: '/admin/customers/:id',
    additionalDataValidator: {
      customer_tag_ids: z.array(z.string()).nullish(),
    },
  },

  {
    matcher: '/admin/customers*',
    middlewares: [extendedCustomerMiddleware],
  },

  {
    method: 'POST',
    matcher: '/admin/extended/customers/tags',
    middlewares: [validateAndTransformBody(CreateCustomerTagSchema)],
  },
  {
    method: 'PUT',
    matcher: '/admin/extended/customers/tags/:id',
    middlewares: [validateAndTransformBody(UpdateCustomerTagSchema)],
  },
  {
    method: 'GET',
    matcher: '/admin/extended/customers/tags',
    middlewares: [
      validateAndTransformQuery(ListCustomerTagsParams, {
        defaults: ['id', 'value', 'created_at', 'updated_at'],
        defaultLimit: 10,
      }),
    ],
  },
  {
    method: 'GET',
    matcher: '/admin/extended/customers/tags/:id',
  },
  {
    method: 'DELETE',
    matcher: '/admin/extended/customers/tags/:id',
  },
]

export default {
  routes: customerTagRoutesMiddlewares,
}
