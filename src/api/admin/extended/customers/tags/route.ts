import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from '@medusajs/framework/http'
import { ContainerRegistrationKeys } from '@medusajs/framework/utils'
import {
  createErrorResponse,
  createSuccessResponse,
  sendApiResponse,
} from '../../../../../lib/api-response'
import { createCustomerTagWorkflow } from '../../../../../workflows/customer/tags/create'
import {
  CreateCustomerTagValidated,
  ListCustomerTagsParamsType,
} from './validators'

// List customer tags
export async function GET(
  req: AuthenticatedMedusaRequest<ListCustomerTagsParamsType>,
  res: MedusaResponse
) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    const { data, metadata } = await query.graph({
      entity: 'customer_tags',
      ...req.queryConfig,
      filters: req.filterableFields,
    })

    return sendApiResponse(
      res,
      createSuccessResponse({
        data,
        count: metadata?.count || 0,
        offset: metadata?.skip || 0,
        limit: metadata?.take || 10,
      })
    )
  } catch (error) {
    return sendApiResponse(
      res,
      createErrorResponse(`Error fetching customer tags: ${error.message}`, 500)
    )
  }
}

// Create a new customer tag
export async function POST(
  req: AuthenticatedMedusaRequest<CreateCustomerTagValidated>,
  res: MedusaResponse
) {
  const logger = req.scope.resolve('logger')
  try {
    const body = req.body

    const { result } = await createCustomerTagWorkflow(req.scope).run({
      input: { value: body.value },
    })

    return sendApiResponse(res, createSuccessResponse(result))
  } catch (error) {
    logger.error('Error creating customer tag:', error)
    return sendApiResponse(
      res,
      createErrorResponse(`Error creating customer tag: ${error.message}`, 500)
    )
  }
}
