import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from '@medusajs/framework/http'
import {
  ContainerRegistrationKeys,
  MedusaError,
} from '@medusajs/framework/utils'
import {
  createErrorResponse,
  createSuccessResponse,
  sendApiResponse,
} from '../../../../../../lib/api-response'
import { deleteCustomerTagWorkflow } from '../../../../../../workflows/customer/tags/delete'
import { updateCustomerTagWorkflow } from '../../../../../../workflows/customer/tags/update'
import { UpdateCustomerTagValidated } from '../validators'

// Get a single customer tag
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const { id } = req.params
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    const { data } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value', 'created_at', 'updated_at'],
      filters: { id },
    })

    if (!data.length) {
      return sendApiResponse(
        res,
        createErrorResponse(`Customer tag with id ${id} not found`, 404)
      )
    }

    return sendApiResponse(res, createSuccessResponse(data[0]))
  } catch (error) {
    return sendApiResponse(
      res,
      createErrorResponse(`Error fetching customer tag: ${error.message}`, 500)
    )
  }
}

// Update an existing customer tag
export async function PUT(
  req: AuthenticatedMedusaRequest<UpdateCustomerTagValidated>,
  res: MedusaResponse
) {
  const logger = req.scope.resolve('logger')
  try {
    const { id } = req.params
    const body = req.body

    const { result } = await updateCustomerTagWorkflow(req.scope).run({
      input: { id, value: body.value },
    })

    return sendApiResponse(res, createSuccessResponse(result))
  } catch (error) {
    logger.error('Error updating customer tag:', error)

    if (error.type === MedusaError.Types.NOT_FOUND) {
      return sendApiResponse(res, createErrorResponse(error.message, 404))
    }

    return sendApiResponse(
      res,
      createErrorResponse(`Error updating customer tag: ${error.message}`, 500)
    )
  }
}

// Delete a customer tag
export async function DELETE(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  const logger = req.scope.resolve('logger')
  try {
    const { id } = req.params

    await deleteCustomerTagWorkflow(req.scope).run({
      input: { id },
    })

    return sendApiResponse(res, createSuccessResponse({ success: true }))
  } catch (error) {
    logger.error('Error deleting customer tag:', error)

    if (error.type === MedusaError.Types.NOT_FOUND) {
      return sendApiResponse(res, createErrorResponse(error.message, 404))
    }

    if (error.type === MedusaError.Types.NOT_ALLOWED) {
      return sendApiResponse(res, createErrorResponse(error.message, 400))
    }

    return sendApiResponse(
      res,
      createErrorResponse(`Error deleting customer tag: ${error.message}`, 500)
    )
  }
}
