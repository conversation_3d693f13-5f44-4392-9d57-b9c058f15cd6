import { model } from '@medusajs/framework/utils'

enum BundleType {
  COMBO = 'combo',
  BYOB = 'byob',
  VARIANT = 'variant',
}

enum DiscountType {
  FLAT_PRICE = 'flat_price',
  PERCENTAGE_OFF = 'percentage_off',
}

export const ExtendedProductVariant = model.define(
  'extended_product_variants',
  {
    id: model.id().primaryKey(),
    discounted_price: model.number().nullable(),
    sdd: model.boolean().default(false).nullable(),
    gtin: model.text().nullable(),
    google_product_category: model.text().nullable(),
    primary_image_id: model.text().nullable(),

    // Bundle Specific Fields
    bundle_type: model.enum(BundleType).nullable(),
    max_bundle_per_order_quantity: model.number().nullable(),
    products_per_bundle: model.number().nullable(),
    online_payment_only: model.boolean().default(false),
    bundle_discount_type: model.enum(DiscountType).nullable(),
    bundle_discount: model.number().nullable(),
  }
)
