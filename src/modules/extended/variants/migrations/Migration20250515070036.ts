import { Migration } from '@mikro-orm/migrations';

export class Migration20250515070036 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" add column if not exists "bundle_type" text check ("bundle_type" in ('combo', 'byob')) null, add column if not exists "max_bundle_per_order_quantity" integer null, add column if not exists "products_per_bundle" integer null, add column if not exists "online_payment_only" boolean not null default false, add column if not exists "bundle_discount_type" text check ("bundle_discount_type" in ('flat_price', 'percentage_off')) null, add column if not exists "bundle_discount" integer null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" drop column if exists "bundle_type", drop column if exists "max_bundle_per_order_quantity", drop column if exists "products_per_bundle", drop column if exists "online_payment_only", drop column if exists "bundle_discount_type", drop column if exists "bundle_discount";`);
  }

}
