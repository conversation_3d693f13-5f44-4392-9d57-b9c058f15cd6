import { Migration } from '@mikro-orm/migrations';

export class Migration20250521115215 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" add column if not exists "is_bundle" boolean not null default false;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" drop column if exists "is_bundle";`);
  }

}
