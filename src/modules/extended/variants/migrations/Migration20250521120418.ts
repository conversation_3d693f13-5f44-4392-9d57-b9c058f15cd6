import { Migration } from '@mikro-orm/migrations';

export class Migration20250521120418 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" drop column if exists "bundle_type", drop column if exists "is_bundle";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" add column if not exists "bundle_type" text check ("bundle_type" in ('combo', 'byob')) null, add column if not exists "is_bundle" boolean not null default false;`);
  }

}
