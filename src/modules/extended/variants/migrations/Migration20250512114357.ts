import { Migration } from '@mikro-orm/migrations'

export class Migration20250512114357 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table if exists "extended_product_variants" add column if not exists "primary_image_id" text null;`
    )
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "extended_product_variants" drop column if exists "primary_image_id";`
    )
  }
}
