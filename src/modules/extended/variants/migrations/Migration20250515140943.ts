import { Migration } from '@mikro-orm/migrations';

export class Migration20250515140943 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`drop index if exists "IDX_extended_product_variants_bundle_type";`);
    this.addSql(`alter table if exists "extended_product_variants" drop column if exists "bundle_type";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" add column if not exists "bundle_type" text check ("bundle_type" in ('combo', 'byob')) null;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_extended_product_variants_bundle_type" ON "extended_product_variants" (bundle_type) WHERE deleted_at IS NULL;`);
  }

}
