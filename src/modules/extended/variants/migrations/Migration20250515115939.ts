import { Migration } from '@mikro-orm/migrations';

export class Migration20250515115939 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" drop column if exists "bundle_type";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_product_variants" add column if not exists "bundle_type" text check ("bundle_type" in ('combo', 'byob')) null;`);
  }

}
