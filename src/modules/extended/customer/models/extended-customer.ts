import { model } from '@medusajs/framework/utils'
import CustomerTag from './customer-tag'

const ExtendedCustomer = model.define('extended_customer', {
  id: model.id().primaryKey(),
  total_orders: model.number().default(0), // DERIVED
  total_sales: model.number().default(0), // DERIVED
  non_expirable_loyalty_points: model.number().default(0), // DERIVED
  expirable_loyalty_points: model.number().default(0), // DERIVED

  notes : model.text().nullable(),
  last_order_created_at : model.dateTime().nullable(),
  type : model.text().nullable(),

  // Editable by Admin
  customer_tags: model.manyToMany(() => CustomerTag, {
    mappedBy: 'extended_customers',
    pivotTable: 'customer_tag_extended_customer',
    joinColumn: 'extended_customer_id',
    inverseJoinColumn: 'customer_tag_id',
  }),
  referred_users_count: model.number().default(0),
})

export default ExtendedCustomer
