import { InjectManager, MedusaContext, MedusaService, Modules } from '@medusajs/framework/utils'
import ExtendedCustomer from './models/extended-customer'
import CustomerTag from './models/customer-tag'
import { Context, CustomerDTO } from '@medusajs/framework/types'

type InjectedDependencies = {
  [Modules.CUSTOMER]: any
}

class ExtendedCustomerService extends MedusaService({
  ExtendedCustomer,
  CustomerTag,
}) {

  protected customerService: any

  constructor(container: any, { [Modules.CUSTOMER]: customerService }: InjectedDependencies) {
    super(...arguments)
    this.customerService = customerService
  }

  @InjectManager()
  async upsertCustomer(
    customer: CustomerDTO,
    @MedusaContext() sharedContext: Context = {}
  ) {
    let existingCustomer: any = null;

    // Try to find existing customer by ID first
    if (customer.id) {
      try {
        existingCustomer = await this.customerService.retrieveCustomer(customer.id, {}, sharedContext);
      } catch (error) {
        // Customer doesn't exist, continue
      }
    }

    // If not found by ID, try by email
    if (!existingCustomer && customer.email) {
      try {
        const customers = await this.customerService.listCustomers({ email: customer.email }, {}, sharedContext);
        if (customers.length > 0) {
          existingCustomer = customers[0];
        }
      } catch (error) {
        // Continue
      }
    }

    // If not found by email, try by phone
    if (!existingCustomer && customer.phone) {
      try {
        const customers = await this.customerService.listCustomers({ phone: customer.phone }, {}, sharedContext);
        if (customers.length > 0) {
          existingCustomer = customers[0];
        }
      } catch (error) {
        // Continue
      }
    }

    if (existingCustomer) {
      // Update existing customer
      return await this.customerService.updateCustomers(existingCustomer.id, customer, sharedContext);
    } else {
      // Create new customer
      return await this.customerService.createCustomers(customer, sharedContext);
    }
  }

}

export default ExtendedCustomerService
