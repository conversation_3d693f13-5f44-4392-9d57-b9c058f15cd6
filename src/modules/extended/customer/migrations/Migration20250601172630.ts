import { Migration } from '@mikro-orm/migrations';

export class Migration20250601172630 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_customer" alter column "type" type text using ("type"::text);`);
    this.addSql(`alter table if exists "extended_customer" alter column "type" drop not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_customer" alter column "type" type text using ("type"::text);`);
    this.addSql(`alter table if exists "extended_customer" alter column "type" set not null;`);
  }

}
