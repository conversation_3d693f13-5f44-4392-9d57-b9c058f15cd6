import { Migration } from '@mikro-orm/migrations';

export class Migration20250531062106 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "extended_customer" add column if not exists "notes" text null, add column if not exists "last_order_created_at" timestamptz null, add column if not exists "type" text not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "extended_customer" drop column if exists "notes", drop column if exists "last_order_created_at", drop column if exists "type";`);
  }

}
