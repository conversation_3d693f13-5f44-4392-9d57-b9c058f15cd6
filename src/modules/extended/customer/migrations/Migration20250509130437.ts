import { Migration } from '@mikro-orm/migrations'

export class Migration20250509130437 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table if not exists "customer_tag" ("id" text not null, "value" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "customer_tag_pkey" primary key ("id"));`
    )
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_customer_tag_deleted_at" ON "customer_tag" (deleted_at) WHERE deleted_at IS NULL;`
    )

    this.addSql(
      `create table if not exists "extended_customer" ("id" text not null, "total_orders" integer not null default 0, "total_sales" integer not null default 0, "non_expirable_loyalty_points" integer not null default 0, "expirable_loyalty_points" integer not null default 0, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "extended_customer_pkey" primary key ("id"));`
    )
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_extended_customer_deleted_at" ON "extended_customer" (deleted_at) WHERE deleted_at IS NULL;`
    )

    this.addSql(
      `create table if not exists "customer_tag_extended_customer" ("extended_customer_id" text not null, "customer_tag_id" text not null, constraint "customer_tag_extended_customer_pkey" primary key ("extended_customer_id", "customer_tag_id"));`
    )

    this.addSql(
      `alter table if exists "customer_tag_extended_customer" add constraint "customer_tag_extended_customer_extended_customer_id_foreign" foreign key ("extended_customer_id") references "extended_customer" ("id") on update cascade on delete cascade;`
    )
    this.addSql(
      `alter table if exists "customer_tag_extended_customer" add constraint "customer_tag_extended_customer_customer_tag_id_foreign" foreign key ("customer_tag_id") references "customer_tag" ("id") on update cascade on delete cascade;`
    )
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "customer_tag_extended_customer" drop constraint if exists "customer_tag_extended_customer_customer_tag_id_foreign";`
    )

    this.addSql(
      `alter table if exists "customer_tag_extended_customer" drop constraint if exists "customer_tag_extended_customer_extended_customer_id_foreign";`
    )

    this.addSql(`drop table if exists "customer_tag" cascade;`)

    this.addSql(`drop table if exists "extended_customer" cascade;`)

    this.addSql(
      `drop table if exists "customer_tag_extended_customer" cascade;`
    )
  }
}
