import { Migration } from '@mikro-orm/migrations'

export class Migration20250512123330 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table if exists "extended_customer" add column if not exists "referred_users_count" integer not null default 0;`
    )
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "extended_customer" drop column if exists "referred_users_count";`
    )
  }
}
