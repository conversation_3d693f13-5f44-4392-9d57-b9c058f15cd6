Starting migration. Total rows: 167
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 4: ERROR - {"message":"Customer address with customer_id: 18532390, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532390, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.933Z"}
Row 3: ERROR - {"message":"Customer address with customer_id: 18532388, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532388, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.940Z"}
Row 1: ERROR - {"message":"Customer address with customer_id: 18532385, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532385, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.943Z"}
Row 2: ERROR - {"message":"Customer address with customer_id: 18532386, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532386, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.949Z"}
Row 6: ERROR - {"message":"Customer address with customer_id: 18532392, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532392, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.958Z"}
Row 5: ERROR - {"message":"Customer address with customer_id: 18532391, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532391, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.959Z"}
Row 7: ERROR - {"message":"Customer address with customer_id: 18532395, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532395, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.961Z"}
Row 10: ERROR - {"message":"Customer address with customer_id: 18532399, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532399, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.964Z"}
Row 8: ERROR - {"message":"Customer address with customer_id: 18532396, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532396, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.967Z"}
Row 9: ERROR - {"message":"Customer address with customer_id: 18532398, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532398, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.968Z"}
Row 17: ERROR - {"message":"Customer address with customer_id: 18532410, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532410, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.996Z"}
Row 12: ERROR - {"message":"Customer address with customer_id: 18532402, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532402, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.997Z"}
Row 11: ERROR - {"message":"Customer address with customer_id: 18532401, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532401, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.998Z"}
Row 18: ERROR - {"message":"Customer address with customer_id: 18532415, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532415, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:02.998Z"}
Row 16: ERROR - {"message":"Customer address with customer_id: 18532408, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532408, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:03.000Z"}
Row 20: ERROR - {"message":"Customer address with customer_id: 18532421, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532421, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:03.000Z"}
Row 13: ERROR - {"message":"Customer address with customer_id: 18532403, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532403, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:03.006Z"}
Row 15: ERROR - {"message":"Customer address with customer_id: 18532406, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532406, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:03.010Z"}
Row 19: ERROR - {"message":"Customer address with customer_id: 18532420, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532420, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:42:03.013Z"}
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 1: ERROR - {"message":"Customer address with customer_id: 18532385, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532385, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.673Z"}
Row 2: ERROR - {"message":"Customer address with customer_id: 18532386, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532386, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.680Z"}
Row 5: ERROR - {"message":"Customer address with customer_id: 18532391, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532391, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.684Z"}
Row 6: ERROR - {"message":"Customer address with customer_id: 18532392, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532392, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.685Z"}
Row 7: ERROR - {"message":"Customer address with customer_id: 18532395, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532395, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.686Z"}
Row 4: ERROR - {"message":"Customer address with customer_id: 18532390, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532390, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.688Z"}
Row 3: ERROR - {"message":"Customer address with customer_id: 18532388, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532388, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.697Z"}
Row 10: ERROR - {"message":"Customer address with customer_id: 18532399, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532399, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.698Z"}
Row 9: ERROR - {"message":"Customer address with customer_id: 18532398, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532398, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.700Z"}
Row 8: ERROR - {"message":"Customer address with customer_id: 18532396, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532396, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.702Z"}
Row 19: ERROR - {"message":"Customer address with customer_id: 18532420, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532420, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.734Z"}
Row 13: ERROR - {"message":"Customer address with customer_id: 18532403, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532403, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.735Z"}
Row 12: ERROR - {"message":"Customer address with customer_id: 18532402, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532402, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.736Z"}
Row 15: ERROR - {"message":"Customer address with customer_id: 18532406, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532406, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.737Z"}
Row 18: ERROR - {"message":"Customer address with customer_id: 18532415, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532415, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.737Z"}
Row 17: ERROR - {"message":"Customer address with customer_id: 18532410, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532410, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.738Z"}
Row 16: ERROR - {"message":"Customer address with customer_id: 18532408, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532408, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.741Z"}
Row 20: ERROR - {"message":"Customer address with customer_id: 18532421, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532421, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.742Z"}
Row 11: ERROR - {"message":"Customer address with customer_id: 18532401, already exists.","name":"Error","stack":"Error: Customer address with customer_id: 18532401, already exists.\n    at dbErrorMapper (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/db-error-mapper.ts:35:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MikroOrmBaseRepository.transaction (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/dal/mikro-orm/mikro-orm-repository.ts:71:12)\n    at async CustomerModuleService.descriptor.value (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/modules-sdk/decorators/inject-transaction-manager.ts:31:14)\n    at async Object.async.container.container (/Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/steps/upsert-customer.ts:33:28)\n    at async Object.invoke (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/workflows-sdk/src/utils/composer/helpers/create-step-handler.ts:79:52)\n    at async DistributedTransaction.handler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/workflow/workflow-manager.ts:214:16)\n    at async stepHandler (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/orchestration/src/transaction/transaction-orchestrator.ts:1028:14)\n    at async Promise.allSettled (index 0)\n    at async promiseAll (/Users/<USER>/Devx/twt/twt-backend/node_modules/@medusajs/utils/src/common/promise-all.ts:27:18)\n⮑ sat /Users/<USER>/Devx/twt/twt-backend/src/workflows/customer/upsert-customer/index.ts: [upsert-customer -> upsert-customer (invoke)]","__isMedusaError":true,"type":"invalid_data","date":"2025-06-01T17:51:20.752Z"}
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 11: ERROR - Cannot read properties of undefined (reading 'id')
Row 17: ERROR - Cannot read properties of undefined (reading 'id')
Row 20: ERROR - Cannot read properties of undefined (reading 'id')
Row 15: ERROR - Cannot read properties of undefined (reading 'id')
Row 13: ERROR - Cannot read properties of undefined (reading 'id')
Row 18: ERROR - Cannot read properties of undefined (reading 'id')
Row 14: ERROR - Cannot read properties of undefined (reading 'id')
Row 12: ERROR - Cannot read properties of undefined (reading 'id')
Row 16: ERROR - Cannot read properties of undefined (reading 'id')
Row 19: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Starting migration. Total rows: 167
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
Starting migration. Total rows: 167
Row 2: ERROR - Cannot read properties of undefined (reading 'id')
Row 1: ERROR - Cannot read properties of undefined (reading 'id')
Row 4: ERROR - Cannot read properties of undefined (reading 'id')
Row 6: ERROR - Cannot read properties of undefined (reading 'id')
Row 5: ERROR - Cannot read properties of undefined (reading 'id')
Row 7: ERROR - Cannot read properties of undefined (reading 'id')
Row 8: ERROR - Cannot read properties of undefined (reading 'id')
Row 9: ERROR - Cannot read properties of undefined (reading 'id')
Row 3: ERROR - Cannot read properties of undefined (reading 'id')
Row 10: ERROR - Cannot read properties of undefined (reading 'id')
Migration complete.
