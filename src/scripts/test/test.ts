import { exec } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { createObjectCsvWriter as createCsvWriter } from 'csv-writer';

const OUTPUT_FILE = path.join(__dirname, 'customers.csv');
const ERROR_LOG = path.join(__dirname, 'errors.log');
let TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kYpsFwj_dqywOhUnw1ZY0V9q5A4bDvpdra2KN2BPgyw'; // Replace with your token

// === Terminal Input ===
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});
const prompt = (query: string) => new Promise<string>((resolve) => rl.question(query, resolve));

// === Base CURL Command ===
const BASE_CURL_COMMAND = (page: number) => `
curl -s 'https://api-enterprise.mydukaan.io/api/store/seller/store-buyer/?ordering=-orders_total_cost&page=${page}&page_size=30' \
-H 'Authorization: Bearer ${TOKEN}' \
-H 'User-Agent: Mozilla/5.0' \
-H 'Accept: application/json' \
-H 'x-Mode: seller-web' \
-H 'x-dukaan-store-id: 202299269' \
--compressed
`;

// === Error Logging ===
async function logError(message: string): Promise<void> {
  const log = `${new Date().toISOString()} - ${message}\n`;
  await fs.appendFile(ERROR_LOG, log);
}

// === cURL Execution ===
async function executeCURL(page: number): Promise<any[]> {
  return new Promise((resolve, reject) => {
    exec(BASE_CURL_COMMAND(page), async (error, stdout, stderr) => {
      if (error) {
        reject(`Error on page ${page}: ${error.message}`);
        return;
      }

      if (stderr && !stderr.trim().startsWith('% Total')) {
        reject(`Stderr on page ${page}: ${stderr}`);
        return;
      }

      try {
        const json = JSON.parse(stdout);
        if (json.detail && json.detail.includes('Invalid token')) {
          reject({ type: '401', message: `Token expired on page ${page}` });
          return;
        }
        if (Array.isArray(json.results)) {
          resolve(json.results);
        } else {
          resolve([]); // Empty results
        }
      } catch (err) {
        reject(`Failed to parse JSON on page ${page}: ${err}`);
      }
    });
  });
}

// === CSV Writer Setup ===
let csvWriter: any;
let isHeaderWritten = false;

async function initCsvWriter(sampleData: any) {
  if (csvWriter) return;

  const headers = Object.keys(sampleData).map((key) => ({ id: key, title: key }));
  csvWriter = createCsvWriter({
    path: OUTPUT_FILE,
    header: headers,
    append: false, // Overwrite on first run
  });

  if (!isHeaderWritten) {
    await fs.writeFile(OUTPUT_FILE, ''); // Clear file
    isHeaderWritten = true;
  }
}

// === Batch Fetch Logic ===
export default async function fetchAllPages() {
  const batchSize = 10;

  // Ask user for starting page
  let inputPage = await prompt('Enter the starting page number (default 1): ');
  inputPage = inputPage.trim();
  let page = 1;
  if (inputPage) {
    const parsed = parseInt(inputPage, 10);
    if (isNaN(parsed) || parsed < 1) {
      console.log(`Invalid input "${inputPage}", starting from page 1.`);
    } else {
      page = parsed;
    }
  }

  console.log('🚀 Starting batch fetch...');

  while (true) {
    console.log(`🔎 Preparing batch starting from page ${page}...`);
    const batchPages = Array.from({ length: batchSize }, (_, i) => page + i);
    console.log(`➡️ Batch pages:`, batchPages);

    if (batchPages.length === 0) {
      console.log('✅ No more pages to fetch.');
      break;
    }

    const promises = batchPages.map((p) =>
      executeCURL(p).catch((err) => {
        console.error(`❌ Error in page ${p}:`, err);
        return [];
      })
    );

    const results = await Promise.all(promises);
    const flatResults = results.flat();

    if (flatResults.length === 0) {
      console.log('✅ No more results. Fetch complete.');
      break;
    }

    // Initialize CSV writer with dynamic headers
    await initCsvWriter(flatResults[0]);

    // Append to CSV
    try {
      await csvWriter.writeRecords(flatResults);
      console.log(`📦 Batch saved (${flatResults.length} records).`);
    } catch (err) {
      await logError(`Failed to write batch: ${err}`);
    }

    page += batchSize;
  }

  console.log(`🎉 Fetch complete.`);
  rl.close(); // Close readline interface when done
}
