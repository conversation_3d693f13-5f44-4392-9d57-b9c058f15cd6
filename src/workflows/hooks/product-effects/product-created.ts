import { createProductsWorkflow } from '@medusajs/medusa/core-flows'
import { ProductVariantDTO } from '@medusajs/framework/types'
import { CreateExtendedVariantWorkflow } from '../../variant/create-extended-variant'
import {
  AdditionalDataStructure,
  createExtendedProductVariantStepType,
  
} from '../../../api/admin/extended/variants/validators'
import createVariantImageWorkflow from '../../variant/create-variant-image'

createProductsWorkflow.hooks.productsCreated(
  async ({ products, additional_data }, { container }) => {
    try {
      if (
        !additional_data ||
        !additional_data.options ||
        !Array.isArray(additional_data.options)
      ) {
        return
      }

      const typedAdditionalData =
        additional_data as unknown as AdditionalDataStructure

      const extendedVariantWorkflow = CreateExtendedVariantWorkflow(container)

      for (const product of products || []) {
        if (!product.variants?.length) {
          continue
        }

        for (const variant of product.variants) {
          if (!variant.options?.length) {
            continue
          }

          const variantOptions = variant.options.map(opt => ({
            title: opt.option?.title || '',
            value: opt.value,
          }))

          let extendedVariantCreated = false

          for (const option of variantOptions) {
            const optionData = typedAdditionalData.options.find(
              data => data[option.title]
            )

            if (!optionData || !optionData[option.title]) {
              continue
            }

            const valueData = optionData[option.title].find(
              item => Object.keys(item)[0] === option.value
            )

            if (!valueData) {
              continue
            }

            const valueKey = Object.keys(valueData)[0]
            const variantExtendedData = valueData[valueKey]?.additional_data

            if (!variantExtendedData) {
              continue
            }

            try {

              console.log("=======> additional data ::", variantExtendedData)

              await extendedVariantWorkflow.run({
                input: {
                  product_variant: variant as ProductVariantDTO,
                  extended_data:
                    variantExtendedData as createExtendedProductVariantStepType,
                },
              })

              // Add variant images if they exist
              if (variantExtendedData.images && variantExtendedData.images.length > 0) {
                await createVariantImageWorkflow(container).run({
                  input: {
                    variant_id: variant.id,
                    images: variantExtendedData.images,
                  },
                })
              }

              extendedVariantCreated = true
              break
            } catch (workflowError) {
              throw workflowError
            }
          }
        }
      }
    } catch (error) {
      throw error
    }
  }
)
