import {
  deleteProductsWorkflow,
  deleteProductVariantsWorkflow,
} from '@medusajs/medusa/core-flows'

deleteProductsWorkflow.hooks.productsDeleted(async ({ ids }, { container }) => {
  // Resolve the product module service
  const productModuleService = container.resolve('product')

  const variants = await productModuleService.listProductVariants(
    { product_id: ids },
    { withDeleted: true }
  )

  const variantIds = variants.map(variant => variant.id)

  if (variantIds.length) {
    await deleteProductVariantsWorkflow(container).run({
      input: { ids: variantIds },
    })
  }
})
