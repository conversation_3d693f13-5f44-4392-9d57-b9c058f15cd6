import {
  deleteProductOptionsWorkflow,
  deleteProductVariantsWorkflow,
} from '@medusajs/medusa/core-flows'

deleteProductOptionsWorkflow.hooks.productOptionsDeleted(
  async ({ ids }, { container }) => {
    const productService = container.resolve('product')

    // 1. Get all variants (optionally, you can filter by product if you know it)
    const variants = await productService.listProductVariants(
      {
        options: { option_id: ids[0] },
      },
      {}
    )

    // 2. Delete all variants
    await deleteProductVariantsWorkflow(container).run({
      input: { ids: variants.map(v => v.id) },
    })
  }
)
