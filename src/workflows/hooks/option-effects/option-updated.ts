import {
  deleteProductVariantsWorkflow,
  updateProductOptionsWorkflow,
} from '@medusajs/medusa/core-flows'

updateProductOptionsWorkflow.hooks.productOptionsUpdated(
  async ({ product_options }, { container }) => {
    const q = container.resolve('query')

    for (const option of product_options) {
      const { data } = await q.graph({
        entity: 'product',
        fields: ['variants.*', 'variants.options.*'],
        filters: {
          options: {
            id: option.id,
          },
        },
      })

      const variants = data[0].variants

      const variantsWithEmptyOptions = variants.filter(
        variant => variant.options.length === 0
      )

      await deleteProductVariantsWorkflow(container).run({
        input: {
          ids: variantsWithEmptyOptions.map(variant => variant.id),
        },
      })
    }
  }
)
