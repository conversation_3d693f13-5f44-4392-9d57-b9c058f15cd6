import { ProductVariantDTO } from '@medusajs/framework/types'
import { updateProductVariantsWorkflow } from '@medusajs/medusa/core-flows'

import { EXTENDED_VARIANT_MODULE } from '../../../modules/extended/variants'
import { ExtendedVariantService } from '../../../modules/extended/variants/service'
import { updateExtendedVariantWorkflow } from '../../variant/update-extended-variant'
import updateVariantImageWorkflow from '../../variant/update-variant-image'
import { UpdateExtendedVariantStepType } from '../../../api/admin/extended/variants/validators'

updateProductVariantsWorkflow.hooks.productVariantsUpdated(
  async ({ product_variants, additional_data }, { container }) => {
    if (!additional_data) {
      return
    }

    const logger = container.resolve('logger')

    for (const product_variant of product_variants) {
      await updateExtendedVariantWorkflow(container).run({
        input: {
          product_variant: product_variant as ProductVariantDTO,
          extended_data: additional_data as UpdateExtendedVariantStepType,
        },
      })
      
      await updateVariantImageWorkflow(container).run({
        input: {
          variant_id: product_variant.id,
          images: additional_data.images as { url: string, rank: number }[],
        },
      })
    }

    logger.info("--> Variant updated effect ran successfully.")
  }
)
