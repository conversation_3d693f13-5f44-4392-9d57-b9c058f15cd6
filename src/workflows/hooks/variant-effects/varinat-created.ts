import { ProductVariantDTO } from '@medusajs/framework/types'
import { createProductVariantsWorkflow } from '@medusajs/medusa/core-flows'

import { CreateExtendedVariantWorkflow } from '../../variant/create-extended-variant'
import { createExtendedProductVariantStepType, createExtendedProductVariantWorkflowType } from '../../../api/admin/extended/variants/validators'
import createVariantImageWorkflow from '../../variant/create-variant-image'

createProductVariantsWorkflow.hooks.productVariantsCreated(
  async ({ product_variants, additional_data }, { container }) => {
    if (!additional_data) {
      return
    };

    (await CreateExtendedVariantWorkflow(container).run({
      input: {
        product_variant: product_variants[0] as ProductVariantDTO,
        extended_data: additional_data as createExtendedProductVariantStepType,
      },
    })) as unknown as createExtendedProductVariantWorkflowType

    await createVariantImageWorkflow(container).run({
      input: {
        variant_id: product_variants[0].id,
        images: (additional_data.images as { url: string; rank: number; }[]),
      },
    })
  }
)
