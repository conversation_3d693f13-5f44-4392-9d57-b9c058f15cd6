import { deleteProductVariantsWorkflow } from '@medusajs/medusa/core-flows'
import { ContainerRegistrationKeys, Modules } from '@medusajs/framework/utils'

import ExtendedProductVariantLink from '../../../links/extended-variant'
import { EXTENDED_VARIANT_MODULE } from '../../../modules/extended/variants'
import { ExtendedVariantService } from '../../../modules/extended/variants/service'

deleteProductVariantsWorkflow.hooks.productVariantsDeleted(
  async ({ ids }, { container }) => {

    const query = container.resolve('query')
    const logger = container.resolve(ContainerRegistrationKeys.LOGGER)
    const { data } = await query.graph({
      entity: ExtendedProductVariantLink.entryPoint,
      fields: ['*'],
      filters: {
        product_variant_id: ids,
      },
    })

    const extendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    ) as ExtendedVariantService

    await extendedVariantService.softDeleteExtendedProductVariants(
      data.map(link => link.extended_product_variants_id)
    )

    const link = container.resolve(ContainerRegistrationKeys.LINK)

    await link.delete({
      [Modules.PRODUCT]: { product_variant_id: ids },
    })

    const { data: variantImages } = await query.graph({
      entity: "variant_image",
      fields: ['*'],
      filters: {
        variant_id: ids
      }
    })

    await extendedVariantService.softDeleteVariantImages(variantImages.map(image => image.id))

    logger.info("--> Variant delete effect ran successfully.")
  }
)
