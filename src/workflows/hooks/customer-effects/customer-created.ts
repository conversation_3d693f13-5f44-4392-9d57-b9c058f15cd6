import { createCustomersWorkflow } from '@medusajs/medusa/core-flows'

import {
  createCustomFromCustomerWorkflow,
  CreateCustomFromCustomerWorkflowInput,
} from '../../customer/create-extended-customer'

createCustomersWorkflow.hooks.customersCreated(
  async ({ customers, additional_data }, { container }) => {
    for (const customer of customers) {
      await createCustomFromCustomerWorkflow(container).run({
        input: {
          customer,
          additional_data,
        } as CreateCustomFromCustomerWorkflowInput,
      })
    }
  }
)
