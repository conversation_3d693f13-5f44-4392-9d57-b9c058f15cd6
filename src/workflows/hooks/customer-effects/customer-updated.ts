import { updateCustomersWorkflow } from '@medusajs/medusa/core-flows'

import {
  updateCustomFromCustomerWorkflow,
  UpdateCustomFromCustomerWorkflowInput,
} from '../../customer/update-extended-customer'

updateCustomersWorkflow.hooks.customersUpdated(
  async ({ customers, additional_data }, { container }) => {
    for (const customer of customers) {
      await updateCustomFromCustomerWorkflow(container).run({
        input: {
          customer,
          additional_data,
        } as UpdateCustomFromCustomerWorkflowInput,
      })
    }
  }
)
