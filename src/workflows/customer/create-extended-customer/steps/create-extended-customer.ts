import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { EXTENDED_CUSTOMER_MODULE } from '../../../../modules/extended/customer'
import ExtendedCustomerService from '../../../../modules/extended/customer/service'

type CreateExtendedCustomerStepInput = {
  customer_tag_ids?: string[]
}

export const createExtendedCustomerStep = createStep(
  'create-extended-customer',
  async (
    { customer_tag_ids }: CreateExtendedCustomerStepInput,
    { container }
  ) => {
    if (!customer_tag_ids || !customer_tag_ids.length)
      return new StepResponse(undefined, undefined)

    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    )

    const extendedCustomer =
      await extendedCustomerService.createExtendedCustomers({
        customer_tags: customer_tag_ids,
      })

    return new StepResponse(extendedCustomer, extendedCustomer.id)
  },
  async (id, { container }) => {
    if (!id) return

    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    )

    await extendedCustomerService.deleteExtendedCustomers([id])
  }
)
