import { CustomerDTO } from '@medusajs/framework/types'
import { Modules } from '@medusajs/framework/utils'
import {
  createWorkflow,
  when,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import { createRemoteLinkStep } from '@medusajs/medusa/core-flows'
import { EXTENDED_CUSTOMER_MODULE } from '../../../modules/extended/customer'
import { checkCustomerTagsExistenceStep } from '../steps/check-customer-tags-existance'
import { createExtendedCustomerStep } from './steps/create-extended-customer'

export type CreateCustomFromCustomerWorkflowInput = {
  customer: CustomerDTO
  additional_data?: { customer_tag_ids?: string[] }
}

export const createCustomFromCustomerWorkflow = createWorkflow(
  'create-custom-from-customer',
  (input: CreateCustomFromCustomerWorkflowInput) => {
    checkCustomerTagsExistenceStep({
      tag_ids: input.additional_data?.customer_tag_ids,
    })

    const extendedCustomer = createExtendedCustomerStep({
      customer_tag_ids: input.additional_data?.customer_tag_ids,
    })

    when(
      { extendedCustomer },
      ({ extendedCustomer }) => extendedCustomer !== undefined
    ).then(() => {
      createRemoteLinkStep([
        {
          [Modules.CUSTOMER]: {
            customer_id: input.customer.id,
          },
          [EXTENDED_CUSTOMER_MODULE]: {
            extended_customer_id: extendedCustomer.id,
          },
        },
      ])
    })

    return new WorkflowResponse({ custom: extendedCustomer })
  }
)
