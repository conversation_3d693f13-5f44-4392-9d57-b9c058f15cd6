import { MedusaError } from '@medusajs/framework/utils'
import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { EXTENDED_CUSTOMER_MODULE } from '../../../../../modules/extended/customer'
import ExtendedCustomerService from '../../../../../modules/extended/customer/service'

type CreateCustomerTagInput = {
  value: string
}

type CreateCustomerTagOutput = {
  id: string
  value: string
  created_at: Date
  updated_at: Date
}

export const createCustomerTagStep = createStep<
  CreateCustomerTagInput,
  CreateCustomerTagOutput,
  { id: string }
>(
  'create-customer-tag-step',
  async (input: CreateCustomerTagInput, { container }) => {
    const { value } = input
    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')
    const query = container.resolve('query')

    logger.info(`Creating customer tag with value: ${value}`)

    const {
      data: [existingTag],
    } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value'],
      filters: { value },
    })

    // Check if tag with this value already exists
    if (existingTag) {
      throw new MedusaError(
        MedusaError.Types.DUPLICATE_ERROR,
        `Customer tag with value '${value}' already exists`
      )
    }

    // Create the new tag
    const customerTag = await extendedCustomerService.createCustomerTags({
      value,
    })

    logger.info(`Created customer tag with id: ${customerTag.id}`)
    return new StepResponse(customerTag, { id: customerTag.id })
  },
  // Compensation function to delete the tag if workflow fails
  async (input: { id: string }, { container }) => {
    if (!input?.id) return

    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')

    logger.info(
      `Deleting customer tag with id: ${input.id} as part of compensation`
    )

    try {
      await extendedCustomerService.deleteCustomerTags([input.id])
      logger.info(
        `Successfully deleted customer tag with id: ${input.id} during compensation`
      )
    } catch (error) {
      logger.error(
        `Failed to delete customer tag during compensation: ${error.message}`
      )
    }
  }
)
