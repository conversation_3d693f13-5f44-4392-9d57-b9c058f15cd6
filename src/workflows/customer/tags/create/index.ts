import {
  createWorkflow,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import { createCustomerTagStep } from './steps/create-customer-tag'

export type CreateCustomerTagInput = {
  value: string
}

export const createCustomerTagWorkflow = createWorkflow(
  'create-customer-tag',
  (input: CreateCustomerTagInput) => {
    const customerTag = createCustomerTagStep(input)

    return new WorkflowResponse({
      customer_tag: customerTag,
    })
  }
)
