import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { MedusaError } from '@medusajs/framework/utils'
import { EXTENDED_CUSTOMER_MODULE } from '../../../../../modules/extended/customer'
import ExtendedCustomerService from '../../../../../modules/extended/customer/service'

type UpdateCustomerTagInput = {
  id: string
  value: string
}

type UpdateCustomerTagOutput = {
  customer_tag: {
    id: string
    value: string
    created_at: Date
    updated_at: Date
  }
}

export const updateCustomerTagStep = createStep<
  UpdateCustomerTagInput,
  UpdateCustomerTagOutput,
  { id: string; previousValue: string }
>(
  'update-customer-tag-step',
  async (input: UpdateCustomerTagInput, { container }) => {
    const { id, value } = input
    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')
    const query = container.resolve('query')

    logger.info(`Updating customer tag with id: ${id}`)

    // Check if tag exists
    const {
      data: [existingTag],
    } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value'],
      filters: { id },
    })

    if (!existingTag) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Customer tag with id '${id}' not found`
      )
    }

    // Check if another tag with the new value already exists
    const {
      data: [duplicateTag],
    } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value'],
      filters: { value },
    })

    if (duplicateTag && duplicateTag.id !== id) {
      throw new MedusaError(
        MedusaError.Types.DUPLICATE_ERROR,
        `Another customer tag with value '${value}' already exists`
      )
    }

    // Update the tag
    const updatedTag = await extendedCustomerService.updateCustomerTags({
      id,
      value,
    })

    logger.info(`Updated customer tag with id: ${id}`)

    return new StepResponse(
      {
        customer_tag: updatedTag,
      },
      { id, previousValue: existingTag.value }
    )
  },
  async (compensation, { container }) => {
    if (!compensation?.id || !compensation?.previousValue) return

    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')

    logger.info(
      `Restoring customer tag with id: ${compensation.id} to previous value: ${compensation.previousValue}`
    )

    try {
      await extendedCustomerService.updateCustomerTags({
        id: compensation.id,
        value: compensation.previousValue,
      })
      logger.info(
        `Successfully restored customer tag with id: ${compensation.id} to previous value during compensation`
      )
    } catch (error) {
      logger.error(
        `Failed to restore customer tag during compensation: ${error.message}`
      )
    }
  }
)
