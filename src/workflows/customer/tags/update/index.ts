import {
  createWorkflow,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import { updateCustomerTagStep } from './steps/update-customer-tag'

export type UpdateCustomerTagInput = {
  id: string
  value: string
}

export const updateCustomerTagWorkflow = createWorkflow(
  'update-customer-tag',
  (input: UpdateCustomerTagInput) => {
    const result = updateCustomerTagStep(input)

    return new WorkflowResponse({
      customer_tag: result.customer_tag,
    })
  }
)
