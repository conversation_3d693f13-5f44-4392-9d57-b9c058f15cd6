import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { MedusaError } from '@medusajs/framework/utils'
import { EXTENDED_CUSTOMER_MODULE } from '../../../../../modules/extended/customer'
import ExtendedCustomerService from '../../../../../modules/extended/customer/service'

type DeleteCustomerTagInput = {
  id: string
}

type DeleteCustomerTagOutput = {
  success: boolean
}

export const deleteCustomerTagStep = createStep<
  DeleteCustomerTagInput,
  DeleteCustomerTagOutput,
  { id: string; value: string }
>(
  'delete-customer-tag-step',
  async (input: DeleteCustomerTagInput, { container }) => {
    const { id } = input
    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')
    const query = container.resolve('query')

    logger.info(`Deleting customer tag with id: ${id}`)

    // Check if tag exists
    const {
      data: [existingTag],
    } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value'],
      filters: { id },
    })

    if (!existingTag) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Customer tag with id '${id}' not found`
      )
    }

    // Check if tag is used by any customers
    const { data: customersUsingTag } = await query.graph({
      entity: 'extended_customer',
      fields: ['id'],
      filters: {
        customer_tags: {
          id: { $eq: id },
        },
      },
    })

    if (customersUsingTag.length > 0) {
      throw new MedusaError(
        MedusaError.Types.NOT_ALLOWED,
        `Cannot delete customer tag as it is used by ${customersUsingTag.length} customers`
      )
    }

    // Delete the tag
    await extendedCustomerService.deleteCustomerTags([id])

    logger.info(`Deleted customer tag with id: ${id}`)

    return new StepResponse(
      {
        success: true,
      },
      { id: id, value: existingTag.value }
    )
  },
  // Compensation function to recreate the tag if needed
  async (compensation, { container }) => {
    if (!compensation?.id || !compensation?.value) return

    const extendedCustomerService: ExtendedCustomerService = container.resolve(
      EXTENDED_CUSTOMER_MODULE
    )
    const logger = container.resolve('logger')

    logger.info(
      `Recreating deleted customer tag with id: ${compensation.id} as part of compensation`
    )

    try {
      // Attempt to recreate the tag with the same ID and value
      await extendedCustomerService.createCustomerTags({
        id: compensation.id,
        value: compensation.value,
      })
      logger.info(
        `Successfully recreated customer tag with id: ${compensation.id} during compensation`
      )
    } catch (error) {
      logger.error(
        `Failed to recreate customer tag during compensation: ${error.message}`
      )
    }
  }
)
