import {
  createWorkflow,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import { deleteCustomerTagStep } from './steps/delete-customer-tag'

export type DeleteCustomerTagInput = {
  id: string
}

export const deleteCustomerTagWorkflow = createWorkflow(
  'delete-customer-tag',
  (input: DeleteCustomerTagInput) => {
    const result = deleteCustomerTagStep(input)

    return new WorkflowResponse({
      success: result.success,
    })
  }
)
