import { CustomerDTO } from '@medusajs/framework/types'
import { Modules } from '@medusajs/framework/utils'
import {
  createWorkflow,
  when,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import {
  createRemoteLinkStep,
  useQueryGraphStep,
} from '@medusajs/medusa/core-flows'
import { checkCustomerTagsExistenceStep } from '../steps/check-customer-tags-existance'
import { createExtendedCustomerStep } from '../create-extended-customer/steps/create-extended-customer'
import { EXTENDED_CUSTOMER_MODULE } from '../../../modules/extended/customer'
import { updateExtendedCustomerStep } from './steps/update-extended-customer'

export type UpdateCustomFromCustomerWorkflowInput = {
  customer: CustomerDTO
  additional_data?: { customer_tag_ids?: string[] }
}

export const updateCustomFromCustomerWorkflow = createWorkflow(
  'update-custom-from-customer',
  (input: UpdateCustomFromCustomerWorkflowInput) => {
    checkCustomerTagsExistenceStep({
      tag_ids: input.additional_data?.customer_tag_ids,
    })

    // @ts-ignore
    const { data: customers } = useQueryGraphStep({
      entity: 'customer',
      fields: ['extended_customer.*'],
      filters: { id: input.customer.id },
    })

    const created = when(
      'create-customer-custom-link',
      { input, customers },
      data =>
        !data.customers[0].extended_customer &&
        (data.input.additional_data?.customer_tag_ids?.length || 0) > 0
    ).then(() => {
      const extendedCustomer = createExtendedCustomerStep({
        customer_tag_ids: input.additional_data?.customer_tag_ids,
      })

      createRemoteLinkStep([
        {
          [Modules.CUSTOMER]: { customer_id: input.customer.id },
          [EXTENDED_CUSTOMER_MODULE]: {
            extended_customer_id: extendedCustomer.id,
          },
        },
      ])

      return extendedCustomer
    })

    const updated = when(
      { input, customers },
      data => !!data.customers[0].extended_customer
    ).then(() => {
      return updateExtendedCustomerStep({
        id: customers[0].extended_customer?.id!,
        customer_tag_ids: input.additional_data?.customer_tag_ids,
      })
    })

    return new WorkflowResponse({ created, updated })
  }
)
