import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { EXTENDED_CUSTOMER_MODULE } from '../../../../modules/extended/customer'
import ExtendedCustomerService from '../../../../modules/extended/customer/service'

type UpdateExtendedCustomerStepInput = {
  id: string
  customer_tag_ids?: string[] | null
}

export const updateExtendedCustomerStep = createStep(
  'update-extended-customer',
  async (
    { id, customer_tag_ids }: UpdateExtendedCustomerStepInput,
    { container }
  ) => {
    if (customer_tag_ids == undefined) return

    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    )

    const prevData = await extendedCustomerService.retrieveExtendedCustomer(id)

    const updatedExtendedCustomer =
      await extendedCustomerService.updateExtendedCustomers({
        id,
        customer_tags: customer_tag_ids || [],
      })

    return new StepResponse(updatedExtendedCustomer, {
      prevCustomerTags: prevData.customer_tags,
      id,
    })
  },
  async (input, { container }) => {
    if (!input?.id) return

    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    )

    await extendedCustomerService.updateExtendedCustomers({
      id: input.id,
      customer_tags: input.prevCustomerTags.map(tag => tag.id) || [],
    })
  }
)
