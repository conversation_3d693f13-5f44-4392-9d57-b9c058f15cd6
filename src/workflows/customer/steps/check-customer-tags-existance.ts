import { MedusaError } from '@medusajs/framework/utils'
import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'

type CheckCustomerTagsInput = {
  tag_ids: string[] | string | undefined
}

type CheckCustomerTagsOutput = {
  existing_tags: { id: string; value: string }[]
  missing_tags: string[]
}

export const checkCustomerTagsExistenceStep = createStep<
  CheckCustomerTagsInput,
  CheckCustomerTagsOutput,
  CheckCustomerTagsInput
>(
  'check-customer-tags-existence-step',
  async (input: CheckCustomerTagsInput, { container }) => {
    if (!input.tag_ids) {
      return new StepResponse({
        existing_tags: [],
        missing_tags: [],
      })
    }

    // Normalize input to array
    const tagIds = Array.isArray(input.tag_ids)
      ? input.tag_ids
      : [input.tag_ids]

    // Skip if no tags provided
    if (!tagIds.length) {
      return new StepResponse({
        existing_tags: [],
        missing_tags: [],
      })
    }

    const query = container.resolve('query')

    // Find existing tags by ID
    const { data: existingTags } = await query.graph({
      entity: 'customer_tag',
      fields: ['id', 'value'],
      filters: { id: { $in: tagIds } },
    })

    // Determine which tag IDs are missing
    const existingTagIds = existingTags.map(tag => tag.id)
    const missingTags = tagIds.filter(id => !existingTagIds.includes(id))

    // If all tags should exist but some are missing, throw an error
    if (missingTags.length > 0) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Customer tags with IDs ${missingTags.join(', ')} do not exist`
      )
    }

    return new StepResponse({
      existing_tags: existingTags,
      missing_tags: missingTags,
    })
  }
)
