import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { EXTENDED_CUSTOMER_MODULE } from "../../../../modules/extended/customer";
import ExtendedCustomerService from "../../../../modules/extended/customer/service";

type UpsertExtendedCustomerStepInput = {
  customer_id: string;
  existing_extended_customer?: any;
  customer_tag_ids?: string[];
};

export const upsertExtendedCustomerStep = createStep(
  'upsert-extended-customer',
  async (
    { customer_id, existing_extended_customer, customer_tag_ids }: UpsertExtendedCustomerStepInput,
    { container }
  ) => {
    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    );
    const logger = container.resolve('logger');

    logger.info(`Upserting extended customer for customer_id: ${customer_id}`);

    // If no customer_tag_ids provided, don't create extended customer
    if (!customer_tag_ids || customer_tag_ids.length === 0) {
      logger.info(`No customer tags provided for customer ${customer_id}, skipping extended customer creation`);
      return new StepResponse(undefined, undefined);
    }

    try {
      let extendedCustomer: any;
      let wasUpdate = false;
      let previousData: any = null;

      if (existing_extended_customer) {
        // Update existing extended customer
        logger.info(`Updating existing extended customer: ${existing_extended_customer.id}`);

        // Store previous data for compensation
        previousData = {
          id: existing_extended_customer.id,
          customer_tags: existing_extended_customer.customer_tags || []
        };

        extendedCustomer = await extendedCustomerService.updateExtendedCustomers({
          id: existing_extended_customer.id,
          customer_tags: customer_tag_ids || [],
        });

        wasUpdate = true;
      } else {
        // Create new extended customer
        logger.info(`Creating new extended customer for customer: ${customer_id}`);

        extendedCustomer = await extendedCustomerService.createExtendedCustomers({
          customer_tags: customer_tag_ids,
          type: 'regular', // Default customer type
        });
      }

      logger.info(`Successfully upserted extended customer: ${extendedCustomer.id}`);

      return new StepResponse(extendedCustomer, {
        extendedCustomerId: extendedCustomer.id,
        wasUpdate,
        previousData
      });

    } catch (error) {
      logger.error(`Error upserting extended customer:`, error);
      throw error;
    }
  },
  async (compensationData, { container }) => {
    if (!compensationData) return;

    const extendedCustomerService = container.resolve<ExtendedCustomerService>(
      EXTENDED_CUSTOMER_MODULE
    );
    const logger = container.resolve('logger');

    try {
      if (compensationData.wasUpdate && compensationData.previousData) {
        // Revert to previous data
        logger.info(`Reverting extended customer update: ${compensationData.extendedCustomerId}`);
        await extendedCustomerService.updateExtendedCustomers({
          id: compensationData.extendedCustomerId,
          customer_tags: (compensationData.previousData.customer_tags || []).map((tag: any) => tag.id),
        });
      } else if (!compensationData.wasUpdate) {
        // Delete the created extended customer
        logger.info(`Deleting created extended customer: ${compensationData.extendedCustomerId}`);
        await extendedCustomerService.deleteExtendedCustomers([compensationData.extendedCustomerId]);
      }
    } catch (error) {
      logger.error(`Error in extended customer upsert compensation:`, error);
    }
  }
);
