import { CustomerDTO } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";
import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk";
import { CustomerDTOWithModifiedAddress } from "../../../../scripts/customer-migration";

export const upsertCustomerStep = createStep(
  'upsert-customer',
  async (customer: CustomerDTOWithModifiedAddress<'id'>, { container }) => {
    const customerService = container.resolve(Modules.CUSTOMER);
    const logger = container.resolve('logger');

    logger.info(`Upserting customer with ID: ${customer.id}`);

    let existingCustomer: any = null;
    let upsertedCustomer: CustomerDTO | null = null;

    try {
      // Only check for existing customer by ID if provided
      if (customer.id) {
        try {
          existingCustomer = await customerService.retrieveCustomer(customer.id);
          logger.info(`Found existing customer by ID: ${customer.id}`);
        } catch (error) {
          // Customer with ID doesn't exist, will create new one
          logger.info(`Customer with ID ${customer.id} not found, will create new customer`);
        }
      }

      if (existingCustomer) {
        // Update existing customer
        logger.info(`Updating existing customer: ${existingCustomer.id}`);
        // Exclude addresses from update data to avoid conflicts
        const { addresses, ...updateData } = customer;
        upsertedCustomer = await customerService.updateCustomers(existingCustomer.id, updateData);
      } else {
        // Create new customer
        logger.info(`Creating new customer`);
        const createData = { ...customer, id: undefined }; // Remove ID for creation

        try {
          upsertedCustomer = await customerService.createCustomers(customer);
        } catch (error: any) {
          // Handle email duplication case
          if (error.message && error.message.includes('email') && error.message.includes('already exists')) {
            logger.info(`Customer with email ${customer.email} already exists. Creating customer without email.`);
            const customerWithoutEmail = { ...createData, email: undefined };
            upsertedCustomer = await customerService.createCustomers(customerWithoutEmail);
          } else {
            throw error;
          }
        }
      }

      if (!upsertedCustomer) {
        throw new Error('Failed to upsert customer');
      }

      logger.info(`Successfully upserted customer: ${upsertedCustomer.id}`);

      return new StepResponse(upsertedCustomer, {
        customerId: upsertedCustomer.id,
        wasUpdate: !!existingCustomer,
        previousData: existingCustomer
      });

    } catch (error) {
      logger.error(`Error upserting customer:`, error);
      throw error;
    }
  },
  async (compensationData, { container }) => {
    if (!compensationData) return;

    const customerService = container.resolve(Modules.CUSTOMER);
    const logger = container.resolve('logger');

    try {
      if (compensationData.wasUpdate && compensationData.previousData) {
        // Revert to previous data
        logger.info(`Reverting customer update: ${compensationData.customerId}`);
        await customerService.updateCustomers(compensationData.customerId, compensationData.previousData);
      } else if (!compensationData.wasUpdate) {
        // Delete the created customer
        logger.info(`Deleting created customer: ${compensationData.customerId}`);
        await customerService.deleteCustomers([compensationData.customerId]);
      }
    } catch (error) {
      logger.error(`Error in customer upsert compensation:`, error);
    }
  }
);
