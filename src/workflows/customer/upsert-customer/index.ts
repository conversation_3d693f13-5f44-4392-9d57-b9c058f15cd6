import { CustomerDTO } from "@medusajs/framework/types";
import { Modules } from "@medusajs/framework/utils";
import {
    createWorkflow,
    when,
    WorkflowResponse,
} from "@medusajs/framework/workflows-sdk";
import {
    createRemoteLinkStep,
    useQueryGraphStep,
} from "@medusajs/medusa/core-flows";
import { checkCustomerTagsExistenceStep } from "../steps/check-customer-tags-existance";
import { EXTENDED_CUSTOMER_MODULE } from "../../../modules/extended/customer";
import { upsertExtendedCustomerStep } from "./steps/upsert-extended-customer";
import { upsertCustomerStep } from "./steps/upsert-customer";
import { CustomerDTOWithModifiedAddress } from "../../../scripts/customer-migration";
// import { testStep } from "./steps/test-step";

export type UpsertCustomerWorkflowInput = {
    customer: CustomerDTOWithModifiedAddress<'id'> & { additional_data: any }
};

export const upsertCustomer = createWorkflow(
    'upsert-customer',
    (input: UpsertCustomerWorkflowInput) => {
        // Validate customer tags if provided
        checkCustomerTagsExistenceStep({
            tag_ids: input.customer.additional_data?.customer_tag_ids,
        });

        // Upsert the main customer record
        const customer = upsertCustomerStep(input.customer);

        // Query for existing extended customer data
        //@ts-ignore
        const { data: customers } = useQueryGraphStep({
            entity: 'customer',
            fields: ['*', 'extended_customer.*'],
            filters: { id: customer.id },
        });

        // testStep(customers);

        // Handle extended customer data if additional_data is provided
        const extendedCustomer = when(
            'handle-extended-customer',
            { input, customers, customer },
            //@ts-ignore
            (data) => !!data.input.customer.additional_data?.customer_tag_ids?.length
        ).then(() => {
            return upsertExtendedCustomerStep({
                customer_id: customer.id,
                existing_extended_customer: customers[0]?.extended_customer,
                customer_tag_ids: input.customer.additional_data?.customer_tag_ids,
            });
        });

        // Create remote link if extended customer was created and no link exists
        when(
            'create-remote-link',
            { customers, extendedCustomer },
            (data) =>
                data.extendedCustomer !== undefined &&
                data.extendedCustomer !== null &&
                !data.customers[0]?.extended_customer
        ).then(() => {
            if (extendedCustomer && extendedCustomer.id) {
                createRemoteLinkStep([
                    {
                        [Modules.CUSTOMER]: { customer_id: customer.id },
                        [EXTENDED_CUSTOMER_MODULE]: {
                            extended_customer_id: extendedCustomer.id,
                        },
                    },
                ]);
            }
        });

        return new WorkflowResponse({
            customer,
            extended_customer: extendedCustomer,
        });
    }
);