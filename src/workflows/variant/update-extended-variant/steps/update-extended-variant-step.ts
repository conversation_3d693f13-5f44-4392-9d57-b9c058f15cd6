import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { UpdateExtendedVariantStepType } from '../../../../api/admin/extended/variants/validators'
import { ExtendedVariantService } from '../../../../modules/extended/variants/service'
import { EXTENDED_VARIANT_MODULE } from '../../../../modules/extended/variants'
import { ExtendedProductVariant } from '../../../../modules/extended/variants/models/extended-product-variant'
import { InferTypeOf } from '@medusajs/framework/types'



export const updateExtendedVariantStep = createStep(
  'update-extended-variant-step',
  async (
    input: {
      extended_product_variants_id: string
      extended_data: UpdateExtendedVariantStepType
    },
    { container }
  ) => {

    console.log(" ======= > additional fied :: ", input)
    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    const prevData =
      await extendedVariantService.retrieveExtendedProductVariant(
        input.extended_product_variants_id
      )

    const result = await extendedVariantService.updateExtendedProductVariants({
      id : input.extended_product_variants_id,
      ...input.extended_data as Omit<InferTypeOf<typeof ExtendedProductVariant>, 'id'>
    })

    return new StepResponse(result, prevData)
  },
  async (prevData, { container }) => {
    if (!prevData) return

    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    await extendedVariantService.updateExtendedProductVariants(prevData)
  }
)
