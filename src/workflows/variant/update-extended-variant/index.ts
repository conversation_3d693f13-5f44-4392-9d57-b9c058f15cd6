import { Modules } from '@medusajs/framework/utils'
import { createWorkflow, transform, when, WorkflowResponse } from '@medusajs/framework/workflows-sdk'
import { createRemoteLinkStep, useQueryGraphStep } from '@medusajs/medusa/core-flows'

import { UpdateExtendedVariantWorkflowInputType } from '../../../api/admin/extended/variants/validators'
import ExtendedVariantLink from '../../../links/extended-variant'
import { EXTENDED_VARIANT_MODULE } from '../../../modules/extended/variants'
import { createExtendedProductStep } from '../create-extended-variant/steps/create-extended-variant-step'
import { updateExtendedVariantStep } from './steps/update-extended-variant-step'

export const updateExtendedVariantWorkflow = createWorkflow(
  'update-extended-variant-workflow',
  (input: UpdateExtendedVariantWorkflowInputType) => {
    //@ts-ignore
    const { data: extended_variants } = useQueryGraphStep({
      entity: ExtendedVariantLink.entryPoint,
      fields: ['*'],
      filters: { product_variant_id: input.product_variant.id },
    })

    // Check if extended variant exists
    const variantExists = transform(
      { extended_variants },
      data => data.extended_variants.length > 0
    )

    // Create extended variant if it doesn't exist
    const created = when(
      { variantExists, input },
      data => !data.variantExists
    ).then(() => {
      return createExtendedProductStep(input.extended_data);
    })

    when(
      { created },
      ({ created }) => created !== undefined
    ).then(() => {
      createRemoteLinkStep([
        {
          [Modules.PRODUCT]: { product_variant_id: input.product_variant.id },
          [EXTENDED_VARIANT_MODULE]: { extended_product_variants_id: created?.id! },
        },
      ])
    })

    // Update extended variant if it exists
    const updated = when(
      { variantExists, extended_variants },
      data => data.variantExists
    ).then(() => {
      const extended_product_variants_id = transform(
        { extended_variants },
        data => data.extended_variants[0].extended_product_variants_id
      )

      return updateExtendedVariantStep({
        extended_product_variants_id,
        extended_data: input.extended_data,
      })
    })

    return new WorkflowResponse({ created, updated })
  }
)
