import { createWorkflow, WorkflowResponse } from "@medusajs/framework/workflows-sdk"

import { updateVariantImageStep } from "./steps/update-variant-image-step"

export const updateVariantImageWorkflow = createWorkflow(
  'update-variant-image',
  (input: {variant_id : string, images : {url : string, rank : number}[] }) => {

    const images = updateVariantImageStep(input)

    return new WorkflowResponse({ images })
  }
)

export default updateVariantImageWorkflow
