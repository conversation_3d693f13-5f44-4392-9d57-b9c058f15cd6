import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk"

import { ExtendedVariantService } from "../../../../modules/extended/variants/service"
import { EXTENDED_VARIANT_MODULE } from "../../../../modules/extended/variants"

type Input = {
  variant_id: string
  images: { url: string, rank: number }[]
}

type StepResult = { id: string; url: string; rank: number; variant_id: string; created_at: Date; updated_at: Date; deleted_at: Date | null; }[]
type CompensationData = { variant_id: string; previousImages: { url: string; rank: number; variant_id: string }[] }

export const updateVariantImageStep = createStep(
  'update-variant-image-step',
  async (input: Input, { container }): Promise<StepResponse<StepResult | null, CompensationData>> => {
    if (!input.images) {
      return new StepResponse(null)
    }

    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    const variantImages = await extendedVariantService.listVariantImages({
      variant_id: input.variant_id
    })

    await extendedVariantService.deleteVariantImages(variantImages.map(image => image.id))

    if (!input.images.length) {
      return new StepResponse(null, {
        variant_id: input.variant_id,
        previousImages: variantImages
      })
    }

    const result = await extendedVariantService.createVariantImages(input.images.map((image) => ({
      url: image.url,
      rank: image.rank,
      variant_id: input.variant_id,
    })))

    return new StepResponse(result, {
      variant_id: input.variant_id,
      previousImages: variantImages
    })
  },
  async (result: CompensationData, { container }) => {
    if (!result?.previousImages) return

    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    // Delete the newly created images
    if (result.variant_id) {
      await extendedVariantService.deleteVariantImages({
        variant_id: result.variant_id,
      })
    }

    // Restore the previous images
    if (result.previousImages?.length) {
      await extendedVariantService.createVariantImages(result.previousImages.map((image) => ({
        url: image.url,
        rank: image.rank,
        variant_id: result.variant_id,
      })))
    }
  }
)
