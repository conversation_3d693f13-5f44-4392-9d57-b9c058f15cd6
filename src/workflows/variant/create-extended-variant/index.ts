import {
  createWorkflow,
  transform,
  when,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'
import { createRemoteLinkStep } from '@medusajs/medusa/core-flows'
import { Modules } from '@medusajs/framework/utils'

import { createExtendedProductStep } from './steps/create-extended-variant-step'
import { EXTENDED_VARIANT_MODULE } from '../../../modules/extended/variants'
import { createVariantImagesStep } from '../create-variant-image/steps/create-variant-images'
import { createExtendedProductVariantWorkflowType } from '../../../api/admin/extended/variants/validators'

export const CreateExtendedVariantWorkflow = createWorkflow(
  'create-extended-product-variant',
  (input: createExtendedProductVariantWorkflowType) => {
    const extended_product_fields = transform(
      { input },
      data => data.input.extended_data
    )

    const extended_product = createExtendedProductStep(extended_product_fields)

    when(
      { extended_product },
      ({ extended_product }) => extended_product !== undefined
    ).then(() => {
      createRemoteLinkStep([
        {
          [Modules.PRODUCT]: { product_variant_id: input.product_variant.id },
          [EXTENDED_VARIANT_MODULE]: {
            extended_product_variants_id: extended_product.id,
          },
        },
      ])
    })

    return new WorkflowResponse(extended_product)
  }
)
