import { createStep, StepResponse } from '@medusajs/framework/workflows-sdk'
import { InferTypeOf } from '@medusajs/framework/types'

import { createExtendedProductVariantStepType } from '../../../../api/admin/extended/variants/validators'
import { ExtendedVariantService } from '../../../../modules/extended/variants/service'
import { EXTENDED_VARIANT_MODULE } from '../../../../modules/extended/variants'
import { ExtendedProductVariant } from '../../../../modules/extended/variants/models/extended-product-variant'

export const createExtendedProductStep = createStep(
  'create-extended-product-step',
  async (input: createExtendedProductVariantStepType, { container }) => {
    if (!input) {
      return
    }

    const extendedProductService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    const custom = await extendedProductService.createExtendedProductVariants({
      ...input as InferTypeOf<typeof ExtendedProductVariant>,
      // @ts-ignore
      bundle_type: input.bundle_type || 'variant',
    })

    return new StepResponse(custom, custom.id)
  },
  async (id: string, { container }) => {
    const extendedProductService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    const custom =
      await extendedProductService.deleteExtendedProductVariants(id)

    return new StepResponse(custom)
  }
)
