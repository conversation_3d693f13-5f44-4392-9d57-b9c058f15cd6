import { createStep, StepResponse } from "@medusajs/framework/workflows-sdk"
import { ExtendedVariantService } from "../../../../modules/extended/variants/service"
import { EXTENDED_VARIANT_MODULE } from "../../../../modules/extended/variants"

type InputType = {
  images: { url: string, rank: number }[],
  variant_id: string
}

export const createVariantImagesStep = createStep(
  'create-variant-images-step',
  async (input: InputType, { container }) => {
    if (!input.images || !input.images.length) {
      return
    }

    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    const result = await extendedVariantService.createVariantImages(input.images.map((image: { url: string, rank: number }) => ({
      url: image.url,
      rank: image.rank,
      variant_id: input.variant_id,
    })))

    return new StepResponse(result, result)
  },
  async (result, { container }) => {
    if (!result || !result.length) {
      return
    }

    const extendedVariantService: ExtendedVariantService = container.resolve(
      EXTENDED_VARIANT_MODULE
    )

    await extendedVariantService.deleteVariantImages(result.map(image => image.id))
  }
)
