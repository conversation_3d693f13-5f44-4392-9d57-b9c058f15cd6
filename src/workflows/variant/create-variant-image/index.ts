import {
  createWorkflow,
  WorkflowResponse,
} from '@medusajs/framework/workflows-sdk'

import { createVariantImagesStep } from './steps/create-variant-images'

export type CreateVariantImageWorkflowInput = {
  variant_id: string
  images: Array<{
    url: string
    rank: number
  }>
}

export const createVariantImageWorkflow = createWorkflow(
  'create-variant-image',
  (input: CreateVariantImageWorkflowInput) => {

    const images = createVariantImagesStep(input)

    return new WorkflowResponse({ images })
  }
)

export default createVariantImageWorkflow

