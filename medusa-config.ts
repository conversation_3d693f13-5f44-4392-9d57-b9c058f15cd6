import { loadEnv, defineConfig } from '@medusajs/framework/utils'

loadEnv(process.env.NODE_ENV || 'development', process.cwd())

module.exports = defineConfig({
  admin: { disable: true },
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || 'supersecret',
      cookieSecret: process.env.COOKIE_SECRET || 'supersecret',
    },
  },
  modules: [
    {
      resolve: '@medusajs/medusa/auth',
      options: {
        providers: [
          {
            resolve: `@devx-commerce/passwordless/providers/passwordless`,
            id: 'passwordless',
            options: {
              smsProviders: [{ provider: 'sns', priority: 1 }],

              smsRateLimitMinutes: 0.5,
              blockDurationMinutes: 5,
              maxAttempts: 3,

              snsOptions: {
                region: process.env.AWS_REGION!,
                accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
              },
            },
          },
          {
            resolve: '@medusajs/medusa/auth-emailpass',
            id: 'emailpass',
            options: {},
          },
        ],
      },
    },
    {
      resolve: './src/modules/extended/customer',
      options: {},
    },
    {
      resolve: './src/modules/extended/variants',
    },
    { resolve: "@medusajs/index", options: {} }
  ],
  plugins: [
    {
      resolve: '@devx-commerce/passwordless',
      options: {},
    },
    {
      resolve: '@devx-commerce/plugin-audit',
      options: {},
    },
    // {
    //   resolve: '@devx-commerce/strapi',
    //   options: {
    //     base_url: process.env.STRAPI_BASE_URL,
    //     api_key: process.env.STRAPI_TOKEN,
    //     default_locale: 'en',
    //   },
    // },
  ],
})
