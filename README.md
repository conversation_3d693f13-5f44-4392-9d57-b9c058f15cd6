# The Whole Truth (TWT) Backend

A Medusa-based e-commerce backend for The Whole Truth's online store, providing a robust platform for managing healthy snacks and food products.

## Overview

This project powers the backend of The Whole Truth's e-commerce platform, built using [Medusa](https://medusajs.com). It handles product management, order processing, customer data, and other essential e-commerce functionalities for The Whole Truth's healthy snack business.

## Prerequisites

- Node.js >= 20
- PostgreSQL
- Yarn or npm

## Getting Started

1. **Clone the repository**

   ```bash
   git clone [your-repository-url]
   cd twt-backend
   ```

2. **Install dependencies**

   ```bash
   yarn install
   # or
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory with the following variables:

   ```
   DATABASE_URL=postgres://localhost/twt-store
   NODE_ENV=development
   JWT_SECRET=your_jwt_secret
   COOKIE_SECRET=your_cookie_secret
   STRIPE_API_KEY=your_stripe_api_key
   ```

4. **Initialize the database**

   ```bash
   yarn seed
   ```

5. **Start the development server**
   ```bash
   yarn dev
   # or
   npm run dev
   ```

## Available Scripts

- `yarn dev` - Start the development server
- `yarn build` - Build the project
- `yarn start` - Start the production server
- `yarn seed` - Seed the database with initial product data
- `yarn test:unit` - Run unit tests
- `yarn test:integration:http` - Run HTTP integration tests
- `yarn test:integration:modules` - Run module integration tests

## Project Structure

- `src/` - Main source code
  - `api/` - API routes and controllers
  - `services/` - Business logic and services
    - `product/` - Product management
    - `order/` - Order processing
    - `customer/` - Customer management
  - `models/` - Database models
  - `scripts/` - Utility scripts
  - `config/` - Configuration files

## Features

- Product Management

  - Healthy snack products catalog
  - Product variants and options
  - Inventory management
  - Product categories and tags

- Order Processing

  - Order management
  - Payment processing
  - Shipping integration
  - Order fulfillment

- Customer Management

  - User authentication
  - Customer profiles
  - Order history
  - Address management

- Additional Features
  - RESTful API endpoints
  - PostgreSQL database integration
  - TypeScript support
  - Automated testing setup
  - Development and production configurations

## API Documentation

The API documentation is available at `/api/docs` when running the server in development mode.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please contact the development team or raise an issue in the repository.
